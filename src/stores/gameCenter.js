import { types } from "mobx-state-tree";

import gameCenter from "../apis/gameCenter";
import deploy from "../config/deploy.config";
import { get, set } from "../utils/storage";

const GameCenterStore = types.model(
  "GameCenterStore",
  {
    VassGameType: types.frozen,
    GameList: types.optional(types.frozen, []),
    hotGames: types.optional(types.frozen, []),
    favGameList: types.optional(types.frozen, { content: [], pageNum: 1, totalPages: 0 }),
    favGames: types.optional(types.frozen, []), // 游戏收藏id
    gameVendor: types.optional(types.frozen, { sorting: [], mapping: {} }),
    RecentGameList: types.frozen,
    LottList: types.frozen,
    ElottList: types.frozen,
    lotteryTime: types.frozen,
    prizeMode: types.optional(types.frozen, ""), // 彩票玩法，传统/盘口
    lottMenus: types.optional(types.frozen, []), // 彩票分类列表
    winnerList: types.optional(types.frozen, []), // 中奖排行榜
    currentGameCategory: types.optional(types.frozen, "HOME"),
    currentVassalage: types.optional(types.frozen, ""),
    currentDataType: types.optional(types.frozen, "ALL"),
    get getLottType() {
      let lottType = [];
      for (const key in this.LottList) {
        lottType[key] = this.LottList[key].displayName;
      }
      return lottType;
    },
    get getElottClass() {
      let classBox = {};
      if (this.ElottList) {
        this.ElottList.games.forEach((item, idx) => {
          classBox[item.gameClassifyNew] = true;
        });
      }
      return Object.keys(classBox).map((item, idx) => {
        return { code: item };
      });
    },
  },
  {
    getWinnerBoard(query) {
      return gameCenter.getWinnerBoard(query).then((res) => {
        const results = res.data.value.list;
        this.setWinnerList(results);
        return res.data.value;
      });
    },
    getGameList(formData, refresh) {
      return gameCenter.getGameList(formData).then((res) => {
        this.setGameList(res.data.value, refresh);
        return res.data.value;
      });
    },
    clearGameList() {
      this.GameList = { games: [] };
    },
    setGameList(GameList, refresh) {
      if (!refresh) {
        this.GameList = GameList;
      } else if (this.GameList && this.GameList.games && this.GameList.games.length > 0) {
        const newTrans = this.GameList.games.concat(GameList.games);
        const newDetails = {
          games: newTrans,
          pageNum: GameList.pageNum,
          totalPages: GameList.totalPages,
        };
        this.GameList = newDetails;
      }
    },
    addFavGame(formData) {
      const nodeId = `${formData.nodeId}`;
      if (!this.favGames.includes(nodeId)) {
        this.favGames = [...this.favGames, nodeId];
        return gameCenter.addFavGame(formData);
      }
      return null;
    },
    removeFavGame(formData) {
      this.favGames = this.favGames.filter((item) => +item !== +formData.nodeId);
      this.favGameList = {
        ...this.favGameList,
        content: this.favGameList.content.filter((item) => +item.nodeId !== +formData.nodeId),
      };
      return gameCenter.removeFavGame(formData);
    },
    getFavGames(formData) {
      return gameCenter.getFavGames(formData).then((res) => {
        this.setFavGames(res.data.value);
        return res.data.value;
      });
    },
    setFavGames(favGames) {
      this.favGames = favGames;
    },
    getFavGameList(formData, loadMore) {
      return gameCenter.getFavGameList(formData).then((res) => {
        this.setFavGameList(res.data.value, loadMore);
        return res.data.value;
      });
    },
    setFavGameList(games, loadMore) {
      if (!loadMore) {
        this.favGameList = games;
      } else {
        const newDetails = {
          content: this.favGameList.content.concat(games.content),
          pageNum: games.pageNum,
          totalPages: games.totalPages,
        };
        this.favGameList = newDetails;
      }
    },
    addRecentGame(formData) {
      return gameCenter.addRecentGame(formData);
    },
    getRecentGameList(formData) {
      return new Promise((resolve) => {
        gameCenter.getRecentGameList(formData).then((res) => {
          this.setRecentGameList(res.data.value);
          resolve(res.data.value);
        });
      });
    },
    setRecentGameList(RecentGameList) {
      this.RecentGameList = RecentGameList;
    },
    async getGameHot(query) {
      const response = await gameCenter.getGameHot(query);
      const data = response?.data?.value?.list;
      this.setGamesHot(data);
      return data;
    },
    setGamesHot(gameHot) {
      this.hotGames = gameHot;
    },
    getGameMenus(query) {
      return gameCenter.getGameMenus(query).then((res) => {
        const results = res?.data || [];
        this.setLottMenus(results);
        return results;
      });
    },
    setLottMenus(menu) {
      this.lottMenus = menu;
    },
    getLottList() {
      if (get("lottList")) {
        this.setLottList(get("lottList"));
      } else {
        return gameCenter.getLottList().then((res) => {
          this.setLottList(Object.values(res.data));
          set("lottList", Object.values(res.data));
        });
      }
    },
    setLottList(LottList) {
      this.LottList = LottList;
    },
    getElottList(formData) {
      if (get("elottList")) {
        this.setElottList(get("elottList"));
      } else {
        return gameCenter.getGameList(formData).then((res) => {
          this.setElottList(res.data.value);
          set("elottList", res.data.value);
        });
      }
    },
    setElottList(ElottList) {
      this.ElottList = ElottList;
    },
    setPrizeMode(mapping) {
      let gameType = "LOTT";
      if (["TCG_LOTTO_VN", "LOTT"].some((item) => !!mapping?.ELOTT?.[item])) {
        gameType = "ELOTT";
      }
      const lottMode = {
        LOTT: deploy.prizeModes.ModeLott,
        ELOTT: deploy.prizeModes.ModeElott,
      };
      this.prizeMode = lottMode[gameType];
    },
    setGameVendor(res) {
      const { sorting = [], mapping = {} } = JSON.parse(JSON.stringify(res));
      this.setPrizeMode(mapping);

      // 记录需要提取的厂商
      const extractConfig = deploy.gameCategoryConfig || {};
      const extractMap = {};
      const extractedVendorsByCategory = {}; // 记录每个分类中被提取的厂商

      // 构建提取映射
      (extractConfig.extractVendors || []).forEach((config) => {
        if (config.enabled) {
          if (!extractMap[config.sourceCategory]) {
            extractMap[config.sourceCategory] = [];
          }
          extractMap[config.sourceCategory].push({
            vendors: config.vendors,
            target: config.targetCategory,
            insertAfter: config.insertAfter,
            removeFromSource: config.removeFromSource !== false, // 默认为true
          });

          // 只有当需要从源分类中移除时才记录
          if (config.removeFromSource !== false) {
            if (!extractedVendorsByCategory[config.sourceCategory]) {
              extractedVendorsByCategory[config.sourceCategory] = [];
            }
            extractedVendorsByCategory[config.sourceCategory].push(...config.vendors);
          }
        }
      });

      // 处理原始数据，添加fullVendors
      let result = [];
      const extractedCategories = [];

      // 处理每个分类
      sorting.forEach((category) => {
        const gameCategory = category.gameCategory;
        const extractRules = extractMap[gameCategory] || [];

        // 创建新的分类对象
        const newCategory = {
          gameCategory,
          gameCount: category.gameCount,
          ordinal: category.ordinal,
          gameClassify: category.gameClassify,
          vendors: [],
          vendorNames: [],
          fullVendors: [],
        };

        // 处理每个厂商
        if (Array.isArray(category.vendorNames)) {
          category.vendorNames.forEach((vendor) => {
            const id = vendor.accountTypeName;

            // 检查是否需要提取
            let shouldExtract = false;
            let targetCategory = null;
            let insertAfter = null;
            let removeFromSource = true;

            for (const rule of extractRules) {
              if (rule.vendors.includes(id)) {
                shouldExtract = true;
                targetCategory = rule.target;
                insertAfter = rule.insertAfter;
                removeFromSource = rule.removeFromSource;
                break;
              }
            }

            // 创建完整的厂商数据
            const fullData = {
              ...vendor,
              ...(category[id] || {}),
              ...(mapping[gameCategory]?.[id] || {}),
            };

            if (shouldExtract) {
              // 找到或创建目标分类
              let targetCat = extractedCategories.find((c) => c.category.gameCategory === targetCategory);

              if (!targetCat) {
                targetCat = {
                  category: {
                    gameCategory: targetCategory,
                    vendors: [],
                    vendorNames: [],
                    fullVendors: [],
                  },
                  insertAfter,
                };
                extractedCategories.push(targetCat);
              }

              // 添加到目标分类
              targetCat.category.vendors.push(id);
              targetCat.category.vendorNames.push(vendor);
              targetCat.category.fullVendors.push({ ...fullData, extracted: true });
              targetCat.category[id] = { ...fullData, extracted: true };

              // 如果不需要从源分类中移除，也添加到原分类
              if (!removeFromSource) {
                newCategory.vendors.push(id);
                newCategory.vendorNames.push(vendor);
                newCategory.fullVendors.push(fullData);
                newCategory[id] = fullData;
              }
            } else {
              // 添加到原分类
              newCategory.vendors.push(id);
              newCategory.vendorNames.push(vendor);
              newCategory.fullVendors.push(fullData);
              newCategory[id] = fullData;
            }
          });
        }

        // 只添加有厂商的分类
        if (newCategory.vendors.length > 0) {
          result.push(newCategory);
        }
      });

      // 插入提取的分类
      extractedCategories.forEach((item) => {
        if (item.insertAfter === "FIRST") {
          // 插入到首位
          result.unshift(item.category);
        } else if (item.insertAfter === "LAST" || !item.insertAfter) {
          // 插入到末尾
          result.push(item.category);
        } else {
          // 插入到指定分类之后
          const insertIndex = result.findIndex((cat) => cat.gameCategory === item.insertAfter);
          if (insertIndex !== -1) {
            result.splice(insertIndex + 1, 0, item.category);
          } else {
            // 如果找不到指定分类，则插入到末尾
            result.push(item.category);
          }
        }
      });

      // 处理分类提取
      if (extractConfig.extractClassify && extractConfig.extractClassify.length > 0) {
        extractConfig.extractClassify.forEach((config) => {
          if (!config.enabled) return;

          const sourceMapping = mapping[config.sourceCategory];
          if (!sourceMapping?.gameClassify?.length) return;

          const classifyItem = sourceMapping.gameClassify.find((item) => item.key === config.classifyKey);
          if (!classifyItem) return;

          // 创建新分类
          const newCategory = {
            gameCategory: config.targetCategory,
            vendors: [],
            vendorNames: [],
            fullVendors: [],
            gameClassify: [classifyItem],
          };

          // 插入新分类
          const insertIndex = result.findIndex((cat) => cat.gameCategory === config.insertAfter);
          if (insertIndex !== -1) {
            result.splice(insertIndex + 1, 0, newCategory);
          } else {
            result.push(newCategory);
          }
        });
      }

      // 构建mapping
      const mappingResult = {};

      // 首先，从原始mapping中复制基本结构，但排除已提取的厂商
      Object.keys(mapping).forEach((category) => {
        mappingResult[category] = {};

        // 复制非厂商属性
        Object.keys(mapping[category]).forEach((key) => {
          // 如果是数组属性，需要特殊处理
          if (["vendors", "vendorNames", "fullVendors"].includes(key)) {
            // 跳过，这些会从result中获取
          } else if (extractedVendorsByCategory[category] && extractedVendorsByCategory[category].includes(key)) {
            // 跳过已提取的厂商
          } else {
            mappingResult[category][key] = mapping[category][key];
          }
        });
      });

      // 然后，从result中获取处理后的数据
      result.forEach((category) => {
        const gameCategory = category.gameCategory;

        // 确保分类存在
        if (!mappingResult[gameCategory]) {
          mappingResult[gameCategory] = {};
        }

        // 复制基本属性
        ["vendors", "vendorNames", "fullVendors", "gameClassify", "gameCount"].forEach((prop) => {
          if (category[prop] !== undefined) {
            mappingResult[gameCategory][prop] = Array.isArray(category[prop]) ? [...category[prop]] : category[prop];
          }
        });

        // 复制厂商数据
        category.vendors?.forEach((id) => {
          if (category[id]) {
            mappingResult[gameCategory][id] = category[id];
          }
        });
      });

      // 最后，确保已提取的厂商不会出现在原分类中（如果配置为移除）
      Object.keys(extractedVendorsByCategory).forEach((category) => {
        if (mappingResult[category]) {
          extractedVendorsByCategory[category].forEach((vendorId) => {
            delete mappingResult[category][vendorId];
          });
        }
      });

      this.gameVendor = {
        sorting: result,
        mapping: mappingResult,
      };
      console.log(this.gameVendor);
    },
    setWinnerList(data = []) {
      const arr = data.map((item) => item.vo);
      arr.sort((a, b) => b.winAmount - a.winAmount);
      this.winnerList = arr;
    },
    setCurrentGameCategory(gameType) {
      this.currentGameCategory = gameType;
    },
    setCurrentVassalage(vassalage) {
      this.currentVassalage = vassalage;
    },
    setCurrentDataType(dataType) {
      this.currentDataType = dataType;
    },
  }
);

export default GameCenterStore;
