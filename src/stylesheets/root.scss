:root {
  // app modal 背景色; 必須rgb代碼
  --s-app-modal-bg-rgb: #{red(#4993fd), green(#4993fd), blue(#4993fd)};
  // app modal 字色
  --s-app-modal-color: #fff;
  --s-logo: url("../assets/images/logo/logo.png");

  --game-list-skeleton-bg: #2d2a39;
  --custom-scrollbar-bg-color: #3b9e7a;
  --overlay-bg: rgb(0 0 0 / 50%);
  --overlay-backdrop-filter: blur(6px);
  --popup-duration: 250ms;
  --popup-timing-function: cubic-bezier(0.4, 0, 0.2, 1);

  /* 主品牌色 */
  --color-primary: #1778ff;
  --color-highlight: #ff0;
  --color-surface: #2d2a39;
  --color-error: #f00;

  /* 文字颜色 */
  --text-color-primary: #fff;
  --text-color-secondary: #071829;
  --text-color-surface: #2d2a39;
  --text-color-accent: var(--color-primary);
  --text-color-highlight: var(--color-highlight);
  --text-color-error: var(--color-error);

  /* 主背景色 */
  --bg-color-primary: #071829;
  --bg-color-secondary: #0d1624;
  --bg-color-surface: var(--color-surface);
  --bg-color-tooltip: #000;
  --bg-color-highlight: var(--color-highlight);
  --bg-color-error: var(--color-error);
  --bg-color-input: var(--bg-color-secondary);

  /* 按钮背景 */
  --bg-color-button-primary: var(--color-primary);
  --bg-color-button-secondary: #c0c0c0;

  /* 菜单/导航按钮背景 */
  --bg-color-menu-default: var(--color-surface);
  --bg-color-menu-active: var(--color-primary);

  /* 渐变 */
  --bg-color-gradient-primary: linear-gradient(0deg, #1778ff 0%, #fff 100%);
  --bg-color-gradient-secondary: linear-gradient(180deg, #fff500 0%, #fff2d6 50%, #ba8800 100%);

  /* 边框 */
  --border-color-default: #fff;
  --border-color-surface: var(--color-surface);
  --border-color-accent: var(--color-primary);
  --border-color-input: var(--color-surface);
  --border-color-input-focus: var(--color-primary);
  --border-color-input-error: var(--color-error);

  --header-height: 120px;
  --header-bg: var(--bg-color-primary);
  --game-menu-height: 140px;
  --mc-header-height: 100px;
  --mc-header-bg: var(--color-primary);
  --mc-header-color: #fff;
  --download-bar-height: 120px;
  --download-bar-bg: #000;
  --footer-height: 120px;
  --footer-bg: linear-gradient(180deg, #02060c 0%, #05122a 100%);

  // a2hs start
  --a2hs-btn-bg-color: var(--bg-color-button-primary);
  // --a2hs-font-color: #fff;
  // --a2hs-bg-color: #eee;
  // --a2hs-btn-color: #fff;
  // --a2hs-btn-cancel-font-color: #fff;
}
