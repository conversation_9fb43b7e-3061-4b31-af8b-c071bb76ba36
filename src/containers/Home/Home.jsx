import React from "react";
import { withRouter } from "react-router";
import { inject, observer } from "mobx-react";
import { withReferral } from "tcg-mobile-common";

import GameMenu from "@/components/GameMenu/GameMenu";
import GameLobby from "@/components/Home/GameLobby";
import VendorList from "@/components/Home/VendorList";
import HomeNav from "@/components/HomeNav/HomeNav";
import HotGame from "@/components/HotGame/HotGame";
import ProviderGameList from "@/components/ProviderGameList/ProviderGameList";
import deploy from "@/config/deploy.config";
import { gamePath } from "@/config/game.config";
import withGame from "@/hoc/withGame";
import { getCssVar } from "@/utils/dom";
import { homeScrollTop } from "@/utils/dom";
import { get, getToken } from "@/utils/storage";

import "./Home.scss";

const { gameSelectType } = deploy;

@inject("mcCommon", "common", "languageShell", "gameCenter", "tcgCommon", "mcMenu", "auth")
@withRouter
@withReferral
@withGame
@observer
class Home extends React.Component {
  get isLogin() {
    return this.props.auth.isLogin;
  }
  get matchs() {
    return this.props.tcgCommon.gameMatches;
  }
  get menus() {
    return [{ gameCategory: "HOME" }, { gameCategory: "HOT" }, ...this.sorting];
  }
  get sorting() {
    return this.props.gameCenter.gameVendor.sorting;
  }
  get mapping() {
    return this.props.tcgCommon.gameVendor.mapping;
  }
  get currentGameCategory() {
    return this.props.gameCenter.currentGameCategory;
  }
  get hotList() {
    return this.props.gameCenter.hotGames;
  }
  get hasDownloadBar() {
    return this.props.tcgCommon.isShowDownloadBar;
  }
  get hotVendorList() {
    const games = this.props.gameCenter.gameVendor.sorting.reduce((prev, next) => {
      return [...prev, ...next.vendorNames.filter((item) => +item.isHot === 1)];
    }, []);
    return games;
  }
  state = { activeIndex: 0 };
  downloadBarHeight = getCssVar("--download-bar-height");
  headerHeight = getCssVar("--header-height");
  componentDidMount() {
    this.getFavoriteGameId();
  }
  initGame(flush = false) {
    this.props.tcgCommon.getGameVendor(flush).then((res) => {
      this.props.gameCenter.setGameVendor(res);
    });
    this.getHotGames(flush);
  }
  getHotGames = (refresh) => {
    const data = {
      isPlatform: 2,
      language: this.props.languageShell.currentLanguage,
      platform: "html5",
    };
    return this.props.gameCenter.getGameHot(data);
  };
  getFavoriteGameId = () => {
    if (this.props.auth.currentToken) {
      this.props.gameCenter.getFavGames({
        token: getToken(),
        pageNo: 1,
        pageSize: 100,
      });
    }
  };
  showVendor(gameCategory) {
    return this.currentGameCategory === gameCategory;
  }
  menuClick = async (gameCategory) => {
    if ([gameSelectType.FAV, gameSelectType.RECENT].includes(gameCategory) && !this.props.auth.currentToken) {
      return this.props.history.push("/m/login");
    }
    await this.props.gameCenter.setCurrentDataType(gameSelectType.ALL);
    await this.props.gameCenter.setCurrentVassalage("");
    await this.props.gameCenter.setCurrentGameCategory(gameCategory);
    homeScrollTop();
  };
  navClick = (index) => {
    this.setState({
      activeIndex: index,
    });
    homeScrollTop();
  };
  moreGame = (gameType) => {
    this.menuClick(gameType);
  };
  checkReferral = () => {
    if (!get("MC_SESSION_INFO")) {
      return true;
    }
    return this.props.showReferral;
  };
  renderContent = (item) => {
    const { vassalage = "", gameCategory } = item;
    if (deploy.gameListType.includes(gameCategory)) {
      return (
        <ProviderGameList
          gameType={gameCategory}
          vassalage={this.props.gameCenter.currentVassalage}
          dataType={this.props.gameCenter.currentDataType}
        />
      );
    }
    if ([gameSelectType.FAV, gameSelectType.RECENT].includes(gameCategory)) {
      return <ProviderGameList gameType={gameCategory} dataType={gameCategory} />;
    }
    switch (gameCategory) {
      case "HOT":
        return <HotGame />;
      case "HOME":
        return <GameLobby />;
      default:
        return (
          <VendorList
            className={gameCategory.toLowerCase()}
            gameType={gameCategory}
            vendors={this.props.gameCenter.gameVendor.mapping[gameCategory]}
          />
        );
    }
  };
  handleSearch = () => {
    this.props.common.showSearchPop(true);
  };
  handleNewGame = () => {
    this.props.history.push(gamePath.NEW);
  };
  handleAnchorScroll = (index) => {
    this.setState({
      activeIndex: index,
    });
  };
  render() {
    return (
      <div className="home-game-enter">
        <HomeNav />
        <div className="home-game-layout">
          <GameMenu
            direction="vertical"
            gameType={this.currentGameCategory}
            menus={this.menus}
            menuClick={this.menuClick}
          />

          <div className="home-game-tabs">
            {this.menus.map((item, idx) => {
              return (
                this.currentGameCategory === item.gameCategory && (
                  <div key={`menu_content_${item.gameCategory}`} className="game-tab-item">
                    {this.renderContent(item)}
                  </div>
                )
              );
            })}
            {this.currentGameCategory === gameSelectType.NEW && <ProviderGameList gameType={gameSelectType.NEW} />}
          </div>
        </div>
      </div>
    );
  }
}

export default Home;
