.download-bar.show-bar + .app-main {
  padding-top: var(--download-bar-height);

  .header-content {
    top: var(--download-bar-height);
  }
}

.download-bar.show-bar {
  & ~ .sidebar {
    .left-side-menu,
    .right-side-menu {
      padding-top: calc(var(--header-height) + var(--download-bar-height));
    }
  }
}

.home-container {
  width: 100%;
  transition: all 0.3s;

  &.app-main {
    background: var(--bg-color-secondary);
  }

  .banner-notice {
    position: relative;
    width: 100%;
    background: #480602;
  }

  .home-content-wrap {
    width: 100%;
    min-height: calc(100vh - var(--header-height) - var(--footer-height));
    padding-top: 8px;
    background: #480602;
  }

  .popup_container_v2 {
    background: var(--overlay-bg);
    backdrop-filter: var(--overlay-backdrop-filter);

    .close-btn {
      top: 24px;
      right: 24px;
      width: 40px;
      height: 40px;
      background: url("~@/assets/images/common/close.png") no-repeat center/40px 40px;
      border-radius: 0;

      .icon {
        display: none;
      }
    }

    .popup_title {
      @include flex-center;
      @include ellipsis;
      width: 100% !important;
      max-width: 100%;
      max-height: unset;
      margin-bottom: 24px;
      font-size: 32px;
      font-weight: 700;
      line-height: normal;
      color: var(--text-color-primary);
      text-align: center;
    }

    .popup_content {
      position: relative;
      max-height: min(calc(var(--vh, 1vh) * 100 - 200px), 800px) !important;
      padding: 26px 24px 90px !important;
      overflow: visible;
      background: var(--bg-color-secondary);
      border-radius: 0 !important;

      .wysiwyg {
        padding: 0 !important;
      }

      .swiper-slide {
      }
    }

    .text {
      font-size: 24px;
      color: var(--text-color-primary);
    }

    .popup_body {
      width: 640px !important;
      max-height: min(calc(var(--vh, 1vh) * 100 - 200px), 800px) !important;
      padding: 0 !important;

      .swiper-slide {
        img {
          display: block;
          width: 100%;
        }
      }

      .nav-left {
        left: -30px;
      }

      .nav-right {
        right: -30px;
      }

      .nav-left,
      .nav-right {
        z-index: 100000;
        display: none;
        width: 60px;
        height: 60px;
        padding: 10px;
        background: #fff !important;
        border-radius: 100px;
        transform: translateY(0) !important;

        .arrow-icon {
          display: block;
          width: 32px;
          height: 32px;
          fill: #118eea;
        }
      }

      .active-btn {
        background: rgb(0 0 0 / 50%);
      }

      .disable-btn {
        background: rgb(0 0 0 / 50%);
      }
    }

    .shell-popup-footer {
      position: absolute;
      bottom: 26px;
      left: 0;
      width: 100%;

      .popup-nav-title {
        @include trim(2);
        @include flex-center;
        height: 120px;
        padding: 20px 11.12px;
        font-size: 30px;
        font-weight: 600;
        line-height: normal;
        color: #fff;
        text-align: center;
        background: linear-gradient(270deg, #33befd 0%, #67e875 100%);
        border-radius: 0 0 20px 20px;
      }
    }

    .popup-nav {
      display: flex;
      gap: 24px;
      align-items: center;
      padding: 0 24px;
    }

    .popup-nav-left,
    .popup-nav-right {
      color: var(--text-color-primary);
      background: transparent;
      border: none;

      &:disabled {
        color: var(--text-color-surface);
      }

      .nav-icon {
        display: block;
        width: 40px;
        height: 40px;
        object-fit: contain;
      }
    }

    .popup-nav-left {
      left: -80px;
    }

    .popup-nav-right {
      right: -80px;
    }

    .popup_pagination_footer {
      display: none;
    }

    .popup_main_header {
      border-radius: 0;
    }

    .shell-popup-header {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 2;
      display: flex;
      justify-content: center;
      width: 100%;
      height: 54px;

      .popup-logo {
        display: block;
        height: 67px;
      }
    }

    .popup-pagenation {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 40px;

      .popup-dot {
        @include flex-center;
        flex-shrink: 0;
        width: 44px;
        height: 44px;
        margin: 0 30px;
        text-transform: capitalize;
        background: rgb(255 255 255 / 30%);
        border: 3px solid rgb(255 255 255 / 20%);
        border-radius: 50px;

        &.on {
          background: linear-gradient(180deg, #00b3ff 0%, #36c0f6 100%);
          border: 3px solid #fff;
          border-radius: 50px;
        }
      }
    }

    .btn-confirm {
      @include flex-center;
      position: fixed;
      bottom: 0;
      left: 0;
      z-index: 1;
      width: 100%;
      height: 112px;
      font-size: 36px;
      line-height: 54px;
      color: #6b000c;
      text-align: center;
      background: linear-gradient(270deg, #ebab51 0%, #fbe38c 100%);
    }

    .today-bar {
      position: absolute;
      bottom: -55px;
      left: 50%;
      margin: 0;
      transform: translateX(-50%);

      .am-checkbox-wrapper {
        @include flex-center;
      }

      .am-checkbox {
        display: block;

        .am-checkbox-inner {
          background: rgb(0 0 0 / 20%);
          border-color: #fff;
          border-radius: 6px;

          &::after {
            right: 14px;
            border-color: #fff;
          }
        }
      }

      .am-list-content {
        font-size: 24px;
        line-height: normal;
        color: #fff;
        white-space: nowrap;
      }
    }
  }
}
