._container_box {
  .download-bar {
    z-index: $download-bar-index !important;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 100px !important;
    padding: 0 25px;
    color: #fff;
    background: #31090e;

    .download-bar-left {
      display: flex;
      gap: 24px;
      align-items: center;
    }

    .download-bar-icon {
      position: relative;
      width: 80px;
      height: 80px;
      overflow: hidden;
      background-color: transparent;
      border-radius: 20px;

      img {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .download-bar-right {
      display: flex;
      gap: 30px;
      align-items: center;
    }

    .download-bar-btn {
      @include flex-center;
      position: relative;
      width: 150px;
      height: 64px;
      padding: 10px;
      font-size: 24px;
      font-weight: 400;
      line-height: 1.21em;
      color: #000;
      text-align: center;
      cursor: pointer;
      background: linear-gradient(180deg, #fcf5bf 0%, #d1ae00 100%);
      border: 2px solid #e8ce77;
      border-radius: 20px;
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.02);
      }
    }

    .download-bar-close {
      position: relative;
      width: 40px;
      height: 40px;
      cursor: pointer;
      background: url("./images/border-close.svg") no-repeat center/100% 100%;
      border-radius: 50%;
      transition: transform 0.2s ease;

      .am-icon {
        display: block;
        width: 100%;
        height: 100%;
        color: #31090e;
      }
    }

    .download-bar-content {
      display: flex;
      flex-direction: column;
      gap: 6px;
    }

    .app-full-name-wrap {
      font-size: 28px;
      font-weight: 400;
      line-height: 1.21em;
      color: #fff;
      white-space: nowrap;
    }

    .app-star-grade {
      display: flex;
      gap: 8px;
      align-items: center;
      align-self: stretch;
      height: fit-content;

      .grade-text {
        display: none;
      }

      .star-icon {
        width: 20px;
        height: 20px;
        background: url("./images/star.svg") no-repeat center/cover;
      }
    }
  }

  .app-win {
    display: none;
  }
}
