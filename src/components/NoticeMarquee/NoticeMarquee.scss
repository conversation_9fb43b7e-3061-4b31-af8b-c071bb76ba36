.notice-root {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  width: 710px;
  height: 70px;
  margin: 0 auto 20px;

  .notice-bg {
    position: relative;
    z-index: 2;
    display: flex;
    flex: 1;
    gap: 20px;
    align-items: center;
    height: 100%;
    padding: 10px 0;
    overflow: hidden;
    background: transparent;
    border-radius: 50px;
    box-shadow: 0 5px 10px 0 rgb(0 0 0 / 10%);
  }

  .notice-icon {
    flex-shrink: 0;
    width: 34px;
    height: 34px;

    .icon-notice {
      display: block;
      width: 22.31px;
      height: 34px;
      margin-left: 5.84px;
      fill: #dab002;
    }

    img,
    .am-icon {
      display: block;
      width: 32px;
      height: 32px;
      object-fit: contain;
    }
  }

  .btn-download {
    @include flex-center;
    flex-shrink: 0;
    flex-direction: column;
    gap: 4px;
    width: 63px;
    height: 63px;
    margin-left: 14px;
    font-size: 16px;
    font-weight: 600;
    line-height: normal;
    color: #fff;
    text-align: center;
    background: var(--bg-color-button-primary);
    border-radius: 100px;

    .icon-download {
      display: block;
      width: 25px;
      height: 24px;
      object-fit: contain;
    }
  }

  .btn-message {
    position: relative;
    flex-shrink: 0;
    margin-left: 15px;

    .icon-msg {
      display: block;
      width: 50px;
      height: 50px;
    }
  }

  .notice-tip {
    margin-right: 10px;
  }

  .marquee {
    position: relative;
    display: flex;
    flex: 1;
    align-items: center;
    height: 100%;
    padding: 0 3px 0 0;
    overflow: hidden;
    font-family: Inter, sans-serif;
    font-size: 24px;
    font-weight: 400;
    line-height: 1.21em;
    color: #fff;

    .marquee_content {
      position: relative;
      white-space: nowrap;
      will-change: right;

      &.empty {
        width: 100%;
        margin-left: 0;
        text-align: center;
        will-change: initial;
      }
    }

    .content-item {
      display: inline-block;
      margin-right: 10px;
      white-space: nowrap;
      cursor: pointer;

      &.empty {
        cursor: default;
      }
    }
  }

  .marquee table {
    border: 0 !important;
  }

  .marquee tbody {
    border: 0 !important;
  }

  .marquee td {
    border: 0 !important;
  }

  .marquee tfoot {
    border: 0 !important;
  }

  .marquee th {
    border: 0 !important;
  }

  .marquee thead {
    border: 0 !important;
  }

  .marquee tr {
    border: 0 !important;
  }

  .marquee tt {
    border: 0 !important;
  }
}
