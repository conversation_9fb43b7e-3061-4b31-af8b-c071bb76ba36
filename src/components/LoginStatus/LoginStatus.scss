.login-status-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 750px;
  height: 96px;
  margin: 0 auto;

  // 未登录状态
  &.not-logged-in {
    .auth-button {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 375px;
      height: 96px;
      padding: 10px;
      font-family: Inter, sans-serif;
      font-weight: 600;
      font-size: 30px;
      line-height: 1.21em;
      text-align: center;
      cursor: pointer;
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.02);
      }

      span {
        display: block;
      }
    }

    .register-btn {
      background: linear-gradient(
        180deg,
        #fcf5bf 0%,
        #d1ae00 100%
      );
      border: 2px solid #e8ce77;
      color: #000000;
    }

    .login-btn {
      background: #b4140a;
      color: #ffffff;
    }
  }

  // 已登录状态
  &.logged-in {
    background: #e8ce77;
    padding: 0 24px;
    gap: 30px;

    .user-info {
      display: flex;
      align-items: center;
      gap: 30px;
      flex: 1;

      .user-details {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .username {
          font-family: Inter, sans-serif;
          font-weight: 600;
          font-size: 24px;
          line-height: 1.21em;
          color: #000000;
        }

        .balance {
          display: flex;
          align-items: center;
          gap: 4px;
          font-family: Inter, sans-serif;
          font-weight: 700;
          font-size: 20px;
          line-height: 1.21em;
          color: #000000;

          .currency {
            opacity: 0.8;
          }

          .amount {
            font-weight: 700;
          }
        }
      }

      .user-avatar {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 64px;
        height: 64px;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 14px;
        cursor: pointer;
        transition: transform 0.2s ease;

        &:hover {
          transform: scale(1.05);
        }

        .avatar-placeholder {
          font-family: Inter, sans-serif;
          font-weight: 700;
          font-size: 24px;
          line-height: 1;
          color: #000000;
          text-transform: uppercase;
        }
      }
    }

    .logout-btn {
      font-family: Inter, sans-serif;
      font-weight: 400;
      font-size: 20px;
      line-height: 1.21em;
      color: #b4140a;
      cursor: pointer;
      padding: 8px 16px;
      border-radius: 8px;
      transition: background-color 0.2s ease;

      &:hover {
        background: rgba(180, 20, 10, 0.1);
      }
    }
  }
}
