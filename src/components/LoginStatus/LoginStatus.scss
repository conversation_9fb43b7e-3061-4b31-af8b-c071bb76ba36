.login-status-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 750px;
  height: 96px;
  margin: 0 auto;

  // 未登录状态
  &.not-logged-in {
    .auth-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 375px;
      height: 96px;
      padding: 10px;
      font-size: 30px;
      font-weight: 600;
      line-height: 1.21em;
      text-align: center;
      cursor: pointer;
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.02);
      }

      span {
        display: block;
      }
    }

    .register-btn {
      color: #000;
      background: linear-gradient(180deg, #fcf5bf 0%, #d1ae00 100%);
      border: 2px solid #e8ce77;
    }

    .login-btn {
      color: #fff;
      background: #b4140a;
    }
  }

  // 已登录状态
  &.logged-in {
    gap: 30px;
    padding: 0 24px;
    background: #e8ce77;

    .user-info {
      display: flex;
      flex: 1;
      gap: 30px;
      align-items: center;

      .user-details {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .username {
          font-size: 24px;
          font-weight: 600;
          line-height: 1.21em;
          color: #000;
        }

        .balance {
          display: flex;
          gap: 4px;
          align-items: center;
          font-size: 20px;
          font-weight: 700;
          line-height: 1.21em;
          color: #000;

          .currency {
            opacity: 0.8;
          }

          .amount {
            font-weight: 700;
          }
        }
      }

      .user-avatar {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 64px;
        height: 64px;
        cursor: pointer;
        background: rgb(0 0 0 / 10%);
        border-radius: 14px;
        transition: transform 0.2s ease;

        &:hover {
          transform: scale(1.05);
        }

        .avatar-placeholder {
          font-size: 24px;
          font-weight: 700;
          line-height: 1;
          color: #000;
          text-transform: uppercase;
        }
      }
    }

    .logout-btn {
      padding: 8px 16px;
      font-size: 20px;
      font-weight: 400;
      line-height: 1.21em;
      color: #b4140a;
      cursor: pointer;
      border-radius: 8px;
      transition: background-color 0.2s ease;

      &:hover {
        background: rgb(180 20 10 / 10%);
      }
    }
  }
}
