import React from "react";
import { with<PERSON>outer } from "react-router-dom";
import { inject, observer } from "mobx-react";

import "./LoginStatus.scss";

@inject("auth", "languageShell", "common", "wallet", "tcgCommon")
@withRouter
@observer
class LoginStatus extends React.Component {
  handleLogin = () => {
    this.props.history.push("/m/login");
  };

  handleRegister = () => {
    this.props.history.push("/m/register");
  };

  handleLogout = () => {
    this.props.auth.logout();
  };

  handleProfile = () => {
    this.props.history.push("/m/member");
  };

  getUsername() {
    const { memberInfo } = this.props.common;
    if (!memberInfo) {
      return "";
    }
    return memberInfo.nickname ? memberInfo.nickname : memberInfo.account;
  }

  getBalance() {
    const { wallet } = this.props.wallet;
    if (!wallet || !wallet.sumBalance) {
      return "0.00";
    }
    return parseFloat(wallet.sumBalance).toFixed(2);
  }

  render() {
    const { isLogin } = this.props.auth;
    const { memberInfo } = this.props.common;
    const username = this.getUsername();
    const balance = this.getBalance();

    if (isLogin && memberInfo) {
      // 已登录状态
      return (
        <div className="login-status-wrap logged-in">
          <div className="user-info">
            <div className="user-details">
              <div className="username">{username}</div>
              <div className="balance">
                <span className="currency">{this.props.tcgCommon.currencySymbol}</span>
                <span className="amount">{balance}</span>
              </div>
            </div>
            <div className="user-avatar" onClick={this.handleProfile}>
              <div className="avatar-placeholder">{username ? username.charAt(0).toUpperCase() : "U"}</div>
            </div>
          </div>
          <div className="logout-btn" onClick={this.handleLogout}>
            LOGOUT
          </div>
        </div>
      );
    }

    // 未登录状态
    return (
      <div className="login-status-wrap not-logged-in">
        <div className="auth-button register-btn" onClick={this.handleRegister}>
          <span>Join Now</span>
        </div>
        <div className="auth-button login-btn" onClick={this.handleLogin}>
          <span>Login</span>
        </div>
      </div>
    );
  }
}

export default LoginStatus;
