import React from "react";
import cz from "classnames";
import { inject, observer } from "mobx-react";

import deploy from "@/config/deploy.config";

import JackPotNum from "./JackpotNum";

import "./Jackpot.scss";
@inject("languageShell", "wallet", "tcgCommon", "gameCenter")
@observer
class JackPot extends React.Component {
  state = { gameList: [] };
  componentDidMount() {
    // this.getGameList();
  }
  getGameList = () => {
    this.props.gameCenter
      .getGameList({
        merchant: deploy.merchant,
        platform: "html5",
        gameName: "",
        gameType: "RNG",
        pageNo: 1,
        pageSize: 18,
        vassalage: "",
        gameClassify: "JP",
        minBet: "",
        minLine: "",
        language: this.props.languageShell.language,
      })
      .then((res) => {
        this.setState({
          gameList: res.games,
        });
      });
  };
  render() {
    return (
      <div className={cz("jackpot-wrap", this.props.className)}>
        {/* <div className="jackpot-title">{this.props.languageShell.t("in_accumulative_prize")}</div> */}
        <div className="jackpot-content">
          <img className="jackpot-bg" src={require("@/assets/images/bg/jackpot-bg.png")} alt="" />
          <div className="jackpot-box">
            <div className="jackpot-num">
              {/* <span className="symbol">{this.props.tcgCommon.currencySymbol}</span> */}
              <JackPotNum minNum={10000000} maxNum={15000000} decimal={0} numHeight={100} animationType="translate" />
            </div>
          </div>
          {/* <div className="jackpot-game-wrap">
            <div className="jackpot-game-swiper" ref={(c) => (this.gameSwiper = c)}>
              <div className="jackpot-game-list swiper-wrapper">
                {this.state.gameList.map((item, index) => {
                  return (
                    <div className="swiper-slide" key={`jp_${index}`}>
                      <div className={`jp-game-item`} onClick={() => this.launchGame(item)}>
                        <div className="game-background">
                          <LazyLoadImage
                            className="img-loading"
                            src={item.showIcon}
                            errorSrc={this.props.common.defaultImage}
                          />
                        </div>
                        <div className="game-item-info">
                          <div className="game-amount">
                            <span className="symbol">{this.props.tcgCommon.currencySymbol}</span>
                            <span>{this.getAmount()}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div> */}
        </div>
      </div>
    );
  }
}

export default JackPot;
