.home-header-wrap {
  position: relative;
  flex-shrink: 0;
  width: 100%;
  height: var(--header-height);
  color: var(--text-color-primary);
  transition: all 0.3s;

  .header-logo {
    display: flex;
    align-items: center;
    height: 100%;

    img {
      display: block;
      width: 266px;
      height: 150px;
      object-fit: contain;
    }
  }

  .header-content {
    position: fixed;
    top: 0;
    left: 0;
    z-index: $header-index;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: var(--header-height);
    padding: 0 24px;
    background: #480602;
    transition: top 0.3s;

    .header-center {
      @include flex-center;
      position: absolute;
      top: 0;
      left: 50%;
      z-index: 1;
      height: 100%;
      transform: translateX(-50%);
    }

    .header-right {
      position: relative;
      display: flex;
      align-items: center;
      height: 100%;
    }

    .header-left {
      position: relative;
      display: flex;
      align-items: center;
    }

    .header-menu {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 44px;
      height: 44px;
      cursor: pointer;

      .icon-menu {
        display: block;
        width: 44px;
        height: 35.06px;
        object-fit: contain;
        fill: #fff;
      }
    }

    .header-right-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 44px;
      height: 44px;

      .icon-right {
        display: block;
        width: 44px;
        height: 37.81px;
        object-fit: contain;
        fill: #fff;
      }
    }
  }
}
