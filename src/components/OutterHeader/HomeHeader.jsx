import React from "react";
import { withRouter } from "react-router-dom";
import { Icon } from "antd-mobile";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import { CommonLogo, withCs } from "tcg-mobile-common";

import deploy from "@/config/deploy.config";
import { get } from "@/utils/storage";

import "./HomeHeader.scss";

const { gameSelectType } = deploy;

@inject("languageShell", "wallet", "tcgCommon", "common", "auth", "gameCenter", "mcCommon")
@withRouter
@withCs
@observer
class HomeHeader extends React.Component {
  get inboxUnreadCount() {
    return this.props.common.inboxUnreadCount;
  }
  get vipLabelName() {
    return this.props.tcgCommon.vipLabelName;
  }
  get showVipBenefit() {
    return this.props.tcgCommon.showVipBenefit;
  }
  get isLogin() {
    return this.props.auth.isLogin;
  }
  componentDidMount() {
    if (get("MC_SESSION_INFO")) {
      this.props.common.renew(false);
    }
    this.props.tcgCommon.setAppWinMounted(true);
  }
  getUsername() {
    const { memberInfo } = this.props.common;
    if (!memberInfo) {
      return "";
    }
    const name = memberInfo.nickname ? memberInfo.nickname : memberInfo.account;
    return name;
  }
  showDownloadPopup = () => {
    this.props.common.setDownPopup(true);
  };
  showSideMenu = () => {
    this.props.common.showSideMenu(!this.props.common.sideMenuOpen);
  };

  goToHome = async () => {
    this.props.gameCenter.setCurrentDataType(gameSelectType.ALL);
    this.props.gameCenter.setCurrentVassalage("");
    await this.props.gameCenter.setCurrentGameCategory("HOME");
    await this.props.history.push(`/m/home`);
    setTimeout(() => {
      window.scrollTo(0, 0);
    }, 0);
  };

  toggleRightMenu = () => {
    this.props.common.showRightMenu(!this.props.common.rightMenuOpen);
  };

  render() {
    return (
      <div className={cz("home-header-wrap")}>
        <div className="header-content" id="home-header-content">
          <div className="header-left">
            <div className={cz("header-menu", { on: this.props.common.sideMenuOpen })} onClick={this.showSideMenu}>
              <Icon className="icon-menu" type={require("!svg-sprite-loader!@/assets/images/header/menu-icon.svg")} />
            </div>
          </div>
          <div className="header-center">
            <div className="header-logo" onClick={this.goToHome}>
              <CommonLogo className="popup-logo" src={require("@/assets/images/logo/logo-new.png")} />
            </div>
          </div>
          <div className="header-right">
            <div className="header-right-icon" style={{ opacity: 0 }}>
              <Icon className="icon-right" type={require("!svg-sprite-loader!@/assets/images/header/menu-icon.svg")} />
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default HomeHeader;
