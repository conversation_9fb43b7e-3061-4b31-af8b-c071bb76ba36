.download-bar.show-bar + .home-container {
  .game-menu-wrap {
    &.menu-vertical {
      .game-menu-scroll {
        max-height: calc(
          100 * var(--vh, 1vh) - var(--header-height) - var(--footer-height) - var(--download-bar-height)
        );
      }
    }
  }
}

.game-menu-wrap {
  flex-shrink: 0;

  &.menu-horizontal {
    width: 100%;
    height: fit-content;

    .game-menu-scroll {
      display: flex;
      align-items: center;
      width: 100%;
      padding: 50px 30px 16px;
      overflow: auto;
    }
  }

  &.menu-vertical {
    position: sticky;
    top: var(--header-height);
    z-index: 1;
    width: $game-menu-width;

    .game-menu-scroll {
      display: flex;
      flex-direction: column;
      gap: 14px;
      width: 100%;
      height: fit-content;
      max-height: calc(100 * var(--vh, 1vh) - var(--header-height) - var(--footer-height));
      overflow-y: auto;
    }
  }

  .game-menu-item {
    display: flex;
    flex-shrink: 0;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 97px;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    color: var(--text-color-primary);
    text-align: center;
    text-transform: uppercase;
    letter-spacing: -0.614px;

    .game-menu-name {
      @include flex-center;

      span {
        display: block;
        width: 100%;
      }
    }

    &.on {
      color: var(--text-color-accent);

      .icon-active {
        display: block;
        margin-top: -25px;
      }

      .icon-normal {
        display: none;
      }
    }

    .game-menu-icon {
      position: relative;

      img {
        display: block;
        height: 60px;
      }
    }

    .icon-normal {
      display: block;
      width: 58px;
      height: 58px;
      margin-bottom: -4px;
      object-fit: contain;
    }

    .icon-active {
      display: none;
      width: 70px;
      height: 70px;
      margin-bottom: 6px;
      object-fit: contain;
    }
  }
}
