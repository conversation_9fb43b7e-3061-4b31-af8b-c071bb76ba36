.game-menu-wrap {
  width: 100%;
  padding: 0;
}

.game-menu-content {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 0 60px;

  .menu-nav {
    @include flex-center;
    position: absolute;
    top: 50%;
    z-index: 10;
    width: 48px;
    height: 48px;
    background: var(--bg-color-menu-active);
    border-radius: 8px;
    transform: translateY(-50%);

    .am-icon {
      display: block;
      width: 32px;
      height: 32px;
      object-fit: contain;
    }

    &.swiper-button-disabled {
      background: var(--bg-color-menu-default);
    }
  }

  .nav-prev {
    left: 0;
  }

  .nav-next {
    right: 0;
  }

  .game-menu-swiper {
    width: 100%;
    overflow: hidden;
  }

  .game-menu-scrollbar {
    width: 48px;
    height: 8px;
    margin: 20px auto 0;
    background-color: rgb(151 132 191 / 50%);
    border-radius: 4px;

    .swiper-scrollbar-drag {
      background: linear-gradient(180deg, #fff5c6 0%, #d1ae4c 100%);
    }
  }

  .game-menu-list {
    width: 100%;

    .game-menu-item {
      position: relative;
      flex-shrink: 0;
      width: 140px;
      height: 120px;
      font-family: Inter, sans-serif;
      font-size: 24px;
      font-weight: 400;
      line-height: 1.21em;
      color: #fff;
      text-align: center;
      cursor: pointer;

      &.on {
        .menu-item-inner {
          background: linear-gradient(180deg, #fcf5bf 0%, #d1ae00 100%);
          border: 2px solid #e8ce77;
          border-radius: 20px;
          box-shadow: inset 0 -5px 5px 0 rgb(0 0 0 / 15%);

          .game-menu-name span {
            color: #000;
          }
        }
      }

      &:not(.on) {
        .menu-item-inner {
          background: radial-gradient(circle at center, #5e0803 0%, #31090e 100%);
          border-radius: 20px;
          box-shadow: 0 5px 10px 0 rgb(49 9 14 / 100%);

          .game-menu-name span {
            color: #fff;
            opacity: 0.5;
          }
        }
      }

      .menu-item-inner {
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
        gap: 10px;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        padding: 0;
      }

      .game-menu-name {
        @include flex-center;

        span {
          display: block;
          width: 100%;

          @include ellipsis;
        }
      }

      .game-menu-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 5px 5px 0 rgb(0 0 0 / 25%);

        img,
        .am-icon {
          display: block;
          width: 60px;
          height: 60px;
          object-fit: stretch;
        }
      }
    }
  }
}
