import React from "react";
import { CommonLogo } from "tcg-mobile-common";

import "./Banner.scss";

class Banner extends React.Component {
  render() {
    return (
      <div className="banner-wrap">
        <div className="banner-background">
          <div className="banner-gradient" />
          <img className="banner-bg-image" src={require("@/assets/images/home/<USER>")} alt="" />
        </div>
        <div className="banner-content">
          <div className="welcome-text">WELCOME TO</div>
          <div className="banner-logo">
            <CommonLogo src={require("@/assets/images/logo/logo-new.png")} />
          </div>
          <div className="banner-effects">
            <div className="effect-circle effect-1" />
            <div className="effect-circle effect-2" />
          </div>
          <div className="banner-character">
            <img src={require("@/assets/images/home/<USER>")} alt="" />
          </div>
        </div>
      </div>
    );
  }
}

export default Banner;
