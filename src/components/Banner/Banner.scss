.banner-wrap {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;

  .banner-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #230301;

    .banner-gradient {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 1) 0%,
        rgba(255, 255, 255, 0) 100%
      );
      z-index: 1;
    }

    .banner-bg-image {
      position: absolute;
      top: -9px;
      left: 184px;
      width: 642.13px;
      height: 301px;
      object-fit: fill;
      z-index: 0;
    }
  }

  .banner-content {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 2;

    .welcome-text {
      position: absolute;
      top: 70px;
      left: 81px;
      width: 249px;
      height: 44px;
      font-family: Inter, sans-serif;
      font-weight: 700;
      font-size: 36px;
      line-height: 1.21em;
      color: transparent;
      background: linear-gradient(
        180deg,
        #fcf5e3 19.32%,
        #ac864f 79.55%
      );
      background-clip: text;
      -webkit-background-clip: text;
    }

    .banner-logo {
      position: absolute;
      top: 105px;
      left: 72px;
      width: 172.6px;
      height: 97px;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .banner-effects {
      .effect-circle {
        position: absolute;
        background: #ffec98;
        border-radius: 50%;
        filter: blur(85px);
      }

      .effect-1 {
        top: -71px;
        left: 242px;
        width: 262.97px;
        height: 262.97px;
      }

      .effect-2 {
        top: -71px;
        left: 536px;
        width: 262.97px;
        height: 262.97px;
      }
    }

    .banner-character {
      position: absolute;
      top: -39px;
      left: 318px;
      width: 399.41px;
      height: 318px;
      z-index: 3;

      img {
        width: 100%;
        height: 100%;
        object-fit: stretch;
      }
    }
  }
}
