.footer-menu {
  position: fixed;
  bottom: 0;
  z-index: $footer-index;
  width: 100%;
  color: var(--text-color-primary);

  .footer-menu-bg {
    position: relative;
    width: 100%;
    height: calc(120px + var(--safe-area-inset-bottom));
    background: #1d0b00;
    border-top: none;
    box-shadow: 0 0 10px 0 rgb(0 0 0 / 15%);
  }

  .footer-menu-list {
    display: flex;
    flex-wrap: wrap;
    height: 100%;
  }

  .footer-menu-item {
    position: relative;
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 6px;
    align-items: center;
    justify-content: center;
    width: 150px;
    height: 120px;
    font-size: 24px;
    font-weight: 400;
    line-height: 1.21em;
    text-align: center;
    cursor: pointer;
    background: linear-gradient(135deg, #f2890b 5%, #ee3a22 92.5%);

    &.on {
      .footer-icon {
        background: linear-gradient(180deg, #fcf5bf 0%, #d1ae00 100%);
        background-clip: text;

        .am-icon {
          background: linear-gradient(180deg, #fcf5bf 0%, #d1ae00 100%);
          background-clip: text;
          fill: transparent;
        }
      }

      .footer-menu-name {
        color: transparent;
        background: linear-gradient(180deg, #fcf5bf 0%, #d1ae00 100%);
        background-clip: text;
      }
    }

    &:not(.on) {
      .footer-icon .am-icon {
        fill: rgb(255 255 255 / 40%);
      }

      .footer-menu-name {
        color: rgb(255 255 255 / 40%);
      }
    }

    .footer-icon {
      position: relative;
      display: flex;
      justify-content: center;

      .am-icon,
      img {
        display: block;
        width: 40px;
        height: 40px;
        object-fit: contain;
      }

      .icon-active {
        display: none;
      }

      .footer-invite {
        @include flex-center;
        position: absolute;
        top: -69px;
        left: -40px;
        width: 120px;
        height: 120px;
        background: #fff;
        border: 4px solid #0095ff;
        border-radius: 1000px;

        .icon-invite {
          width: 100px;
          height: 100px;
        }
      }
    }

    .footer-menu-name {
      margin-top: 12px;
    }

    .icon-bonus {
      position: absolute;
      top: -28px;
      right: 0;
      width: 70px;
    }
  }

  .footer-service-btn {
    width: 100%;
    height: 100%;
    padding-top: 20px;
  }

  .chat-tip {
    position: absolute;
    top: 0;
    right: 27px;
    width: 20px;
    height: 20px;
    background: #ea4335;
    border-radius: 100px;
  }

  .footer__chat {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .footer__chat-icon {
    display: block;
    width: 46px;
    height: 46px;
    margin-bottom: 4px;

    .chat-icon-img {
      display: block;
      width: 100%;
      height: 100%;
    }
  }

  .message-notify {
    position: absolute;
    top: 0;
    right: 0;
    width: 10px;
    height: 10px;
    background-color: #ee2622;
    border-radius: 50%;
  }
}
