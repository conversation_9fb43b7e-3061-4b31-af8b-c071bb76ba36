import React from "react";
import { with<PERSON><PERSON><PERSON> } from "react-router-dom";
import { Icon } from "antd-mobile";
import { inject, observer } from "mobx-react";

import "./HomeNav.scss";

@inject("languageShell", "auth")
@withRouter
@observer
class HomeNav extends React.Component {
  handleDeposit = () => {
    if (this.props.auth.isLogin) {
      this.props.history.push("/m/voucherCenter");
    } else {
      this.props.history.push("/m/login");
    }
  };

  handleWithdraw = () => {
    if (this.props.auth.isLogin) {
      this.props.history.push("/m/withdraw");
    } else {
      this.props.history.push("/m/login");
    }
  };

  handleVip = () => {
    if (this.props.auth.isLogin) {
      this.props.history.push("/m/vip");
    } else {
      this.props.history.push("/m/login");
    }
  };

  handleAgent = () => {
    this.props.history.push("/m/join");
  };

  render() {
    return (
      <div className="home-nav-wrap">
        <div className="home-nav-item" onClick={this.handleDeposit}>
          <div className="nav-icon">
            <Icon type={require("!svg-sprite-loader!@/assets/images/home/<USER>")} />
          </div>
          <div className="nav-text">{this.props.languageShell.t("hd_deposit_button")}</div>
        </div>
        <div className="home-nav-item" onClick={this.handleWithdraw}>
          <div className="nav-icon">
            <Icon type={require("!svg-sprite-loader!@/assets/images/home/<USER>")} />
          </div>
          <div className="nav-text">{this.props.languageShell.t("hd_withdraw_button")}</div>
        </div>
        <div className="home-nav-item" onClick={this.handleVip}>
          <div className="nav-icon">
            <Icon type={require("!svg-sprite-loader!@/assets/images/home/<USER>")} />
          </div>
          <div className="nav-text">VIP</div>
        </div>
        <div className="home-nav-item" onClick={this.handleAgent}>
          <div className="nav-icon">
            <Icon type={require("!svg-sprite-loader!@/assets/images/home/<USER>")} />
          </div>
          <div className="nav-text">Agent</div>
        </div>
      </div>
    );
  }
}

export default HomeNav;
