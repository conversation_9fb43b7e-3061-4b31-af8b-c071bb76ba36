.home-nav-wrap {
  display: flex;
  align-items: stretch;
  width: 100%;
  padding: 20px;
  background: linear-gradient(
    180deg,
    #31090e 0%,
    #480602 50%
  );

  .home-nav-item {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
    cursor: pointer;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.05);
    }

    .nav-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 44px;
      height: 44px;
      background: #ffffff;
      border-radius: 4px;

      .am-icon {
        width: 44px;
        height: 44px;
        fill: transparent;
        background: linear-gradient(
          180deg,
          #fcf5bf 0%,
          #d1ae00 100%
        );
        background-clip: text;
        -webkit-background-clip: text;
      }
    }

    .nav-text {
      font-family: Inter, sans-serif;
      font-weight: 600;
      font-size: 28px;
      line-height: 1.21em;
      color: transparent;
      background: linear-gradient(
        180deg,
        #fcf5bf 0%,
        #d1ae00 100%
      );
      background-clip: text;
      -webkit-background-clip: text;
      text-align: center;
    }
  }
}
