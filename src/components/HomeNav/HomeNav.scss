.home-nav-wrap {
  display: flex;
  align-items: stretch;
  width: 100%;
  padding: 20px;
  background: linear-gradient(180deg, #31090e 0%, #480602 50%);

  .home-nav-item {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 10px;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.05);
    }

    .nav-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 44px;
      height: 44px;
      border-radius: 4px;

      .am-icon {
        width: 44px;
        height: 44px;
        background: linear-gradient(180deg, #fcf5bf 0%, #d1ae00 100%);
        background-clip: text;
        fill: transparent;
      }
    }

    .nav-text {
      font-size: 28px;
      font-weight: 600;
      line-height: 1.21em;
      color: transparent;
      text-align: center;
      background: linear-gradient(180deg, #fcf5bf 0%, #d1ae00 100%);
      background-clip: text;
    }
  }
}
