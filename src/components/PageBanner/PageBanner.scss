:root {
  --swiper-pagination-bullet-horizontal-gap: 4px;
  --swiper-pagination-bottom: 16px;
}

.page-banner {
  position: relative;
  width: 100%;

  .swiper {
    width: 100%;
  }

  .swiper-inner {
    width: 100%;
    overflow: hidden;
  }

  .swiper-pagination {
    bottom: var(--swiper-pagination-bottom) !important;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    width: 100%;
  }

  .swiper-pagination-bullet {
    flex-shrink: 0;
    width: 12px;
    height: 12px;
    background: #fff;
    border-radius: 12px;
    opacity: 1;
  }

  .swiper-pagination-bullet-active {
    background: url(~@/assets/images/common/swiper-pagination-active.svg) no-repeat center/100% 100%;
  }

  img {
    display: block;
    width: 100%;
    object-fit: cover;
  }

  // Static banner styles
  &.static-banner {
    height: 250px;
    overflow: hidden;

    .banner-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #230301;

      .banner-gradient {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, rgb(255 255 255 / 100%) 0%, rgb(255 255 255 / 0%) 100%);
      }

      .banner-bg-image {
        position: absolute;
        top: -9px;
        left: 184px;
        z-index: 0;
        width: 642.13px;
        height: 301px;
        object-fit: fill;
      }
    }

    .banner-content {
      position: relative;
      z-index: 2;
      width: 100%;
      height: 100%;

      .welcome-text {
        position: absolute;
        top: 70px;
        left: 81px;
        width: 249px;
        height: 44px;
        font-size: 36px;
        font-weight: 700;
        line-height: 1.21em;
        color: transparent;
        background: linear-gradient(180deg, #fcf5e3 19.32%, #ac864f 79.55%);
        background-clip: text;
      }

      .banner-logo {
        position: absolute;
        top: 105px;
        left: 72px;
        width: 172.6px;
        height: 97px;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      .banner-effects {
        .effect-circle {
          position: absolute;
          background: #ffec98;
          border-radius: 50%;
          filter: blur(85px);
        }

        .effect-1 {
          top: -71px;
          left: 242px;
          width: 262.97px;
          height: 262.97px;
        }

        .effect-2 {
          top: -71px;
          left: 536px;
          width: 262.97px;
          height: 262.97px;
        }
      }

      .banner-character {
        position: absolute;
        top: -39px;
        left: 318px;
        z-index: 3;
        width: 399.41px;
        height: 318px;

        img {
          width: 100%;
          height: 100%;
          object-fit: stretch;
        }
      }
    }
  }
}

@keyframes colorProgress {
  0% {
    width: 0;
  }

  100% {
    width: 50px;
  }
}
