const deployConfig = {
  platform: "mobile", // mobile, desktop, tv
  merchant: "ae6666",
  brandName: "royalmanila.com",
  language: "EN",
  supportLanguages: ["EN", "TY"],
  ageLimit: "18",
  theme: "",
  gateway: {
    WEB: 1,
    MOBILE: 3,
  },
  prizeMode: "Elott",
  prizeModes: {
    ModeLott: "Lott",
    ModeZy: "ZY",
    ModeElott: "Elott",
  },
  esportGames: ["AV", "AI", "IMES", "TF", "IA"], // "AV", "AI", "IMES", "TF", "IA"
  cockfightGames: ["SV3", "DS88", "DIG", "WGB", "WS168"], // "SV3", "DS88", "DIG", "WGB", "WS168"
  inHouse: [],
  gameListType: ["RNG", "FISH", "PVP", "LIVE", "NEW"],
  gameImgSize: "rx2", // rx2 rx1 sx2
  gameSelectType: {
    ALL: "ALL",
    FAV: "FAV",
    HOT: "HOT",
    RECENT: "RECENT",
    NEW: "NEW",
    SEARCH: "SEARCH",
  },
  bannerType: {
    RNG: "m_rng",
    SPORTS: "m_sports",
    PVP: "m_pvp",
    LIVE: "m_live",
    FISH: "m_fish",
    LOTT: "m_lott",
    ELOTT: "m_elott",
    REGISTER: "m_reg",
    LOGIN: "m_login",
    DOWNLOAD: "m_download",
  },
  contentTypes: {
    Agent: "A", //agent annount
    Player: "PL", //player user anount
    Banner: "B", //Banner pic
    Pop_Up: "PU", //propagate pic
    Promotion: "PR", //activity pic
  },
  playTypes: {
    Tradition: "Tradition",
    Entertainment: "Entertainment",
  },
  agentType: [1, 2, 41, 42],
  realUserType: [0, 1, 2],
  ModuleId: {
    COMMON: "COMM3",
    FUNDTRANSFER: "FUNDTRANS3",
    AGEANNOUNCE: "AGEANNOUNCE3",
    Member_Transaction_Report: "MEMTRANS3",
    GAME_BASIC: "COMM3",
    PERSONAL_REPORT: "PERSPNL3",
    AGENT_INFO: "AGEINFO3",
    AGENT_DOWNLINE: "AGEDOWNMAN3",
    REGISTER_REE3: "REG3",
    VALID_CAPTCHA3: "CAPTCHA3",
  },
  error: {},
  gameCategoryConfig: {
    extractVendors: [
      {
        sourceCategory: "SPORTS",
        targetCategory: "ESPORTS",
        vendors: ["AV", "AI", "IMES", "TF", "IA"],
        insertAfter: "SPORTS",
        enabled: true,
        removeFromSource: true,
      },
      {
        sourceCategory: "SPORTS",
        targetCategory: "COCKFIGHT",
        vendors: ["SV3", "DS88", "DIG", "WGB", "WS168"],
        insertAfter: "PVP",
        enabled: true,
        removeFromSource: true,
      },
      {
        sourceCategory: "RNG",
        targetCategory: "INHOUSE",
        vendors: ["PP", "PT"],
        insertAfter: "",
        enabled: true,
        removeFromSource: true,
      },
    ],
    extractClassify: [
      {
        sourceCategory: "RNG",
        targetCategory: "JACKPOT",
        classifyKey: "JP",
        insertAfter: "RNG",
        enabled: false,
        removeFromSource: false,
      },
    ],
  },
};

export default deployConfig;
